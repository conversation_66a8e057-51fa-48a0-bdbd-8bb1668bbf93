"""
AI response generation module.

This module handles generating natural language responses from search results
using various AI providers (Mistral, OpenAI, Groq).
"""

import os
import logging
from typing import Dict, List, Any, Optional
from openai import OpenAI
from mistralai import Mistral

logger = logging.getLogger(__name__)


class ResponseGenerator:
    """Handles AI-powered response generation from search results."""
    
    def __init__(self, response_config: Dict):
        """Initialize the response generator with configuration."""
        self.response_config = response_config
        self.response_client = None
        self.is_mistral = False
        
        # Initialize the appropriate AI client
        self._initialize_client()
        
    def _initialize_client(self):
        """Initialize the AI client based on configuration."""
        try:
            # Get API keys
            mistral_api_key = os.getenv("MISTRAL_API_KEY")
            openai_api_key = os.getenv("OPENAI_API_KEY")
            groq_api_key = os.getenv("GROQ_API_KEY")
            groq_base_url = self.response_config.get('groq_base_url')
            
            # Get the response model name
            response_model = self.response_config.get('model', '')
            
            # Check if we're using a Mistral model for response generation
            if response_model.startswith('mistral-') or response_model.startswith('open-mistral-'):
                if mistral_api_key:
                    logger.info(f"Using Mistral API for response generation with model: {response_model}")
                    self.response_client = Mistral(api_key=mistral_api_key)
                    self.is_mistral = True
                else:
                    logger.error("Mistral API key required for Mistral models")
                    raise ValueError("MISTRAL_API_KEY required for Mistral models")
            
            # Use Groq for response generation if configured
            elif groq_api_key and groq_base_url:
                logger.info("Using Groq API for response generation")
                self.response_client = OpenAI(
                    base_url=groq_base_url,
                    api_key=groq_api_key
                )
                self.is_mistral = False
            
            # Use OpenAI if API key is available
            elif openai_api_key and openai_api_key.strip():
                logger.info("Using OpenAI API for response generation")
                self.response_client = OpenAI(api_key=openai_api_key)
                self.is_mistral = False
            
            # Fallback to Mistral if available
            elif mistral_api_key:
                logger.info("Using Mistral API for response generation as fallback")
                self.response_client = Mistral(api_key=mistral_api_key)
                self.is_mistral = True
            
            else:
                logger.error("No valid API key found for response generation")
                raise ValueError("No valid API key found for response generation")
                
        except Exception as e:
            logger.error(f"Error initializing response client: {str(e)}")
            raise
    
    def generate_response(self, query: str, search_context: Dict) -> str:
        """
        Generate a natural language response based on search results.
        
        Args:
            query: Original search query
            search_context: Dictionary containing search results and metadata
            
        Returns:
            Generated response text
        """
        try:
            if not self.response_client:
                return "Response generation is not available due to missing API configuration."
            
            # Build the prompt
            prompt = self._build_prompt(query, search_context)
            
            # Generate response using the appropriate client
            if self.is_mistral:
                return self._generate_mistral_response(prompt)
            else:
                return self._generate_openai_response(prompt)
                
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return f"I apologize, but I encountered an error while generating a response: {str(e)}"
    
    def _build_prompt(self, query: str, search_context: Dict) -> str:
        """Build the prompt for AI response generation."""
        results = search_context.get("results", [])
        
        if not results:
            return f"""
            User Query: {query}
            
            I don't have any relevant information to answer this query. Please try rephrasing your question or using different keywords.
            """
        
        # Build context from search results
        context_parts = []
        for i, result in enumerate(results[:5], 1):  # Limit to top 5 results
            content = result.content[:1000]  # Limit content length
            entities = [e.name for e in result.entities[:5]]  # Top 5 entities
            
            context_part = f"""
            Source {i}:
            Content: {content}
            Entities: {', '.join(entities) if entities else 'None'}
            """
            context_parts.append(context_part)
        
        context_text = "\n".join(context_parts)
        
        # Build the complete prompt
        prompt = f"""
        You are a helpful AI assistant that answers questions based on provided context. 
        Please provide a comprehensive and accurate answer to the user's query using the information from the sources below.
        
        User Query: {query}
        
        Context from search results:
        {context_text}
        
        Instructions:
        1. Answer the query directly and comprehensively
        2. Use information from the provided sources
        3. If the query is in Chinese, respond in Chinese
        4. If the query is about financial data or stocks, provide specific numbers and details
        5. Organize the information clearly
        6. If you cannot find relevant information, say so clearly
        
        Response:
        """
        
        return prompt
    
    def _generate_mistral_response(self, prompt: str) -> str:
        """Generate response using Mistral API."""
        try:
            model = self.response_config.get('model', 'mistral-large-latest')
            max_tokens = self.response_config.get('max_tokens', 1000)
            temperature = self.response_config.get('temperature', 0.7)
            
            response = self.response_client.chat.complete(
                model=model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Error with Mistral API: {str(e)}")
            raise
    
    def _generate_openai_response(self, prompt: str) -> str:
        """Generate response using OpenAI-compatible API (OpenAI or Groq)."""
        try:
            model = self.response_config.get('model', 'gpt-3.5-turbo')
            max_tokens = self.response_config.get('max_tokens', 1000)
            temperature = self.response_config.get('temperature', 0.7)
            
            response = self.response_client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Error with OpenAI-compatible API: {str(e)}")
            raise
    
    def generate_summary(self, text: str, max_length: int = 200) -> str:
        """
        Generate a summary of the given text.
        
        Args:
            text: Text to summarize
            max_length: Maximum length of the summary
            
        Returns:
            Summary text
        """
        try:
            if not self.response_client:
                return text[:max_length] + "..." if len(text) > max_length else text
            
            prompt = f"""
            Please provide a concise summary of the following text in {max_length} characters or less:
            
            {text}
            
            Summary:
            """
            
            if self.is_mistral:
                response = self.response_client.chat.complete(
                    model=self.response_config.get('model', 'mistral-large-latest'),
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=max_length // 4,  # Rough estimate for token count
                    temperature=0.3
                )
                return response.choices[0].message.content.strip()
            else:
                response = self.response_client.chat.completions.create(
                    model=self.response_config.get('model', 'gpt-3.5-turbo'),
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=max_length // 4,
                    temperature=0.3
                )
                return response.choices[0].message.content.strip()
                
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            return text[:max_length] + "..." if len(text) > max_length else text
    
    def translate_text(self, text: str, target_language: str = "English") -> str:
        """
        Translate text to the target language.
        
        Args:
            text: Text to translate
            target_language: Target language for translation
            
        Returns:
            Translated text
        """
        try:
            if not self.response_client:
                return text
            
            prompt = f"""
            Please translate the following text to {target_language}:
            
            {text}
            
            Translation:
            """
            
            if self.is_mistral:
                response = self.response_client.chat.complete(
                    model=self.response_config.get('model', 'mistral-large-latest'),
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=len(text) * 2,  # Allow for expansion
                    temperature=0.3
                )
                return response.choices[0].message.content.strip()
            else:
                response = self.response_client.chat.completions.create(
                    model=self.response_config.get('model', 'gpt-3.5-turbo'),
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=len(text) * 2,
                    temperature=0.3
                )
                return response.choices[0].message.content.strip()
                
        except Exception as e:
            logger.error(f"Error translating text: {str(e)}")
            return text
    
    def is_available(self) -> bool:
        """
        Check if response generation is available.
        
        Returns:
            True if response generation is available, False otherwise
        """
        return self.response_client is not None
    
    def get_model_info(self) -> Dict:
        """
        Get information about the current model configuration.
        
        Returns:
            Dictionary with model information
        """
        return {
            'provider': 'Mistral' if self.is_mistral else 'OpenAI/Groq',
            'model': self.response_config.get('model', 'unknown'),
            'max_tokens': self.response_config.get('max_tokens', 1000),
            'temperature': self.response_config.get('temperature', 0.7),
            'available': self.is_available()
        }
