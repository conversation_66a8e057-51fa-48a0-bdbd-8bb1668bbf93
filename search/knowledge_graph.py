"""
Knowledge graph operations module.

This module handles entity extraction, relationship discovery,
and knowledge graph traversal for search results.
"""

import logging
from typing import List, Dict
from db import DatabaseConnection
from core.models import Entity, Relationship

logger = logging.getLogger(__name__)


class KnowledgeGraphEngine:
    """Handles knowledge graph operations for search enhancement."""
    
    def __init__(self):
        """Initialize the knowledge graph engine."""
        logger.info("Knowledge graph engine initialized")
    
    def get_entities_for_content(self, db: DatabaseConnection, content: str) -> List[Entity]:
        """
        Extract entities related to the given content.
        
        Args:
            db: Database connection
            content: Content text to find entities for
            
        Returns:
            List of Entity objects found in the content
        """
        try:
            if not content or len(content.strip()) < 10:
                return []
            
            # Search for entities that appear in the content
            # Use a more flexible matching approach
            sql = """
                SELECT DISTINCT e.entity_id, e.name, e.category, e.description
                FROM Entities e
                WHERE e.name IS NOT NULL
                    AND LENGTH(e.name) > 2
                    AND (
                        %s LIKE CONCAT('%', e.name, '%')
                        OR e.name LIKE CONCAT('%', SUBSTRING(%s, 1, 50), '%')
                    )
                ORDER BY LENGTH(e.name) DESC
                LIMIT 20;
            """
            
            results = db.execute_query(sql, (content, content))
            
            if not results:
                logger.debug("No entities found for content")
                return []
            
            entities = []
            for r in results:
                try:
                    entity = Entity(
                        entity_id=r[0],  # entity_id
                        name=r[1],
                        category=r[2] or "CONCEPT",  # category - default to CONCEPT if None
                        description=r[3],  # description field
                        metadata={}  # empty metadata dict for confidence scores
                    )
                    entities.append(entity)

                except Exception as entity_error:
                    logger.warning(f"Error creating entity from result {r}: {entity_error}")
                    continue
            
            logger.debug(f"Found {len(entities)} entities for content")
            return entities
            
        except Exception as e:
            logger.error(f"Error getting entities for content: {str(e)}")
            return []
    
    def get_relationships(self, db: DatabaseConnection, entity_ids: List[int]) -> List[Relationship]:
        """
        Get relationships between the given entities.
        
        Args:
            db: Database connection
            entity_ids: List of entity IDs to find relationships for
            
        Returns:
            List of Relationship objects
        """
        try:
            if not entity_ids:
                return []
            
            # Create a comma-separated string of entity IDs for the SQL query
            ids_str = ','.join(map(str, entity_ids))
            
            # Find relationships where either source or target is in our entity list
            sql = f"""
                SELECT DISTINCT 
                    source_entity_id, 
                    target_entity_id, 
                    relation_type, 
                    doc_id
                FROM Relationships
                WHERE source_entity_id IN ({ids_str})
                   OR target_entity_id IN ({ids_str})
                LIMIT 20;
            """
            
            results = db.execute_query(sql)
            
            if not results:
                logger.debug("No relationships found for entities")
                return []
            
            relationships = []
            for r in results:
                try:
                    relationship = Relationship(
                        source_entity_id=r[0],
                        target_entity_id=r[1],
                        relation_type=r[2] or "related_to",
                        metadata={"doc_id": r[3]} if r[3] else {}
                    )
                    relationships.append(relationship)
                    
                except Exception as rel_error:
                    logger.warning(f"Error creating relationship from result {r}: {rel_error}")
                    continue
            
            logger.debug(f"Found {len(relationships)} relationships for {len(entity_ids)} entities")
            return relationships
            
        except Exception as e:
            logger.error(f"Error getting relationships: {str(e)}")
            return []
    
    def find_related_entities(self, db: DatabaseConnection, entity_id: int, 
                             max_depth: int = 2) -> List[Entity]:
        """
        Find entities related to a given entity through relationships.
        
        Args:
            db: Database connection
            entity_id: Starting entity ID
            max_depth: Maximum relationship depth to traverse
            
        Returns:
            List of related Entity objects
        """
        try:
            visited_entities = set()
            related_entities = []
            current_level = [entity_id]
            
            for depth in range(max_depth):
                if not current_level:
                    break
                
                next_level = []
                
                for current_entity_id in current_level:
                    if current_entity_id in visited_entities:
                        continue
                    
                    visited_entities.add(current_entity_id)
                    
                    # Find directly connected entities
                    sql = """
                        SELECT DISTINCT 
                            CASE 
                                WHEN source_entity_id = %s THEN target_entity_id
                                ELSE source_entity_id
                            END as related_entity_id
                        FROM Relationships
                        WHERE source_entity_id = %s OR target_entity_id = %s
                        LIMIT 10;
                    """
                    
                    results = db.execute_query(sql, (current_entity_id, current_entity_id, current_entity_id))
                    
                    for r in results:
                        related_id = r[0]
                        if related_id not in visited_entities:
                            next_level.append(related_id)
                
                current_level = next_level
            
            # Get entity details for all found IDs
            if visited_entities:
                entity_ids = list(visited_entities)
                entity_ids.remove(entity_id)  # Remove the starting entity
                
                if entity_ids:
                    ids_str = ','.join(map(str, entity_ids))
                    sql = f"""
                        SELECT entity_id, name, category, description
                        FROM Entities
                        WHERE entity_id IN ({ids_str})
                        LIMIT 50;
                    """
                    
                    results = db.execute_query(sql)
                    
                    for r in results:
                        try:
                            entity = Entity(
                                entity_id=r[0],  # entity_id
                                name=r[1],
                                category=r[2] or "CONCEPT",  # category - default to CONCEPT if None
                                description=r[3],  # description field
                                metadata={}  # empty metadata dict for confidence scores
                            )
                            related_entities.append(entity)

                        except Exception as entity_error:
                            logger.warning(f"Error creating related entity: {entity_error}")
                            continue
            
            logger.debug(f"Found {len(related_entities)} related entities for entity {entity_id}")
            return related_entities
            
        except Exception as e:
            logger.error(f"Error finding related entities: {str(e)}")
            return []
    
    def get_entity_by_name(self, db: DatabaseConnection, name: str) -> Entity:
        """
        Find an entity by its name.
        
        Args:
            db: Database connection
            name: Entity name to search for
            
        Returns:
            Entity object if found, None otherwise
        """
        try:
            sql = """
                SELECT entity_id, name, category, description
                FROM Entities
                WHERE name = %s
                LIMIT 1;
            """
            
            results = db.execute_query(sql, (name,))
            
            if not results:
                return None
            
            r = results[0]

            return Entity(
                entity_id=r[0],  # entity_id
                name=r[1],
                category=r[2] or "CONCEPT",  # category - default to CONCEPT if None
                description=r[3],  # description field
                metadata={}  # empty metadata dict for confidence scores
            )
            
        except Exception as e:
            logger.error(f"Error getting entity by name: {str(e)}")
            return None
    
    def get_entities_by_type(self, db: DatabaseConnection, entity_type: str, 
                            limit: int = 20) -> List[Entity]:
        """
        Get entities of a specific type.
        
        Args:
            db: Database connection
            entity_type: Type of entities to retrieve
            limit: Maximum number of entities to return
            
        Returns:
            List of Entity objects of the specified type
        """
        try:
            sql = """
                SELECT entity_id, name, category, description
                FROM Entities
                WHERE category = %s
                ORDER BY name
                LIMIT %s;
            """
            
            results = db.execute_query(sql, (entity_type, limit))
            
            entities = []
            for r in results:
                try:
                    entity = Entity(
                        entity_id=r[0],  # entity_id
                        name=r[1],
                        category=r[2] or "CONCEPT",  # category - default to CONCEPT if None
                        description=r[3],  # description field
                        metadata={}  # empty metadata dict for confidence scores
                    )
                    entities.append(entity)

                except Exception as entity_error:
                    logger.warning(f"Error creating entity: {entity_error}")
                    continue
            
            logger.debug(f"Found {len(entities)} entities of type '{entity_type}'")
            return entities
            
        except Exception as e:
            logger.error(f"Error getting entities by type: {str(e)}")
            return []
    
    def get_relationship_types(self, db: DatabaseConnection) -> List[str]:
        """
        Get all unique relationship types in the knowledge graph.
        
        Args:
            db: Database connection
            
        Returns:
            List of relationship type names
        """
        try:
            sql = """
                SELECT DISTINCT relation_type
                FROM Relationships
                WHERE relation_type IS NOT NULL
                ORDER BY relation_type;
            """
            
            results = db.execute_query(sql)
            
            return [r[0] for r in results if r[0]]
            
        except Exception as e:
            logger.error(f"Error getting relationship types: {str(e)}")
            return []
    
    def get_knowledge_graph_stats(self, db: DatabaseConnection) -> Dict:
        """
        Get statistics about the knowledge graph.
        
        Args:
            db: Database connection
            
        Returns:
            Dictionary with knowledge graph statistics
        """
        try:
            stats = {}
            
            # Count entities
            entity_sql = "SELECT COUNT(*) FROM Entities"
            entity_result = db.execute_query(entity_sql)
            stats['total_entities'] = entity_result[0][0] if entity_result else 0
            
            # Count relationships
            rel_sql = "SELECT COUNT(*) FROM Relationships"
            rel_result = db.execute_query(rel_sql)
            stats['total_relationships'] = rel_result[0][0] if rel_result else 0
            
            # Count entity types
            type_sql = "SELECT COUNT(DISTINCT category) FROM Entities WHERE category IS NOT NULL"
            type_result = db.execute_query(type_sql)
            stats['entity_types'] = type_result[0][0] if type_result else 0
            
            # Count relationship types
            rel_type_sql = "SELECT COUNT(DISTINCT relation_type) FROM Relationships WHERE relation_type IS NOT NULL"
            rel_type_result = db.execute_query(rel_type_sql)
            stats['relationship_types'] = rel_type_result[0][0] if rel_type_result else 0
            
            logger.debug(f"Knowledge graph stats: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error getting knowledge graph stats: {str(e)}")
            return {
                'total_entities': 0,
                'total_relationships': 0,
                'entity_types': 0,
                'relationship_types': 0
            }
