"""
RAG Query System for SingleStore Knowledge Graph.

This module implements a hybrid search system that combines:
1. Vector similarity search
2. Full-text search
3. Knowledge graph traversal
4. Advanced reranking
to answer natural language queries with citations.

This is the main orchestrator that coordinates all search components.
"""

import os
import logging
import json
import time
import re
from typing import Dict, List, Any
from dotenv import load_dotenv
from db import DatabaseConnection
from core.models import Entity, Relationship, SearchResult, SearchResponse
from core.config import config

# Import refactored modules
from search.vector_search import VectorSearchEngine
from search.text_search import TextSearchEngine
from search.knowledge_graph import KnowledgeGraphEngine
from search.reranking import SearchReranker
from search.response_generator import ResponseGenerator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

class RAGQueryEngine:
    """Implements hybrid search combining vector similarity, text search, and knowledge graph."""

    def __init__(self, debug_output: bool = False):
        """
        Initialize the RAG Query Engine.

        Args:
            debug_output: If True, enable debug output mode
        """
        # Load environment variables
        load_dotenv(override=True)
        logger.info("Environment variables loaded")

        # Get configuration
        self.search_config = config.retrieval['search']
        self.response_config = config.retrieval['response_generation']
        self.embedding_config = config.knowledge_creation.get('embeddings', {})

        # Initialize search components
        try:
            self.vector_engine = VectorSearchEngine(self.embedding_config)
            logger.info("Vector search engine initialized")
        except Exception as e:
            logger.error(f"Failed to initialize vector search engine: {e}")
            self.vector_engine = None

        self.text_engine = TextSearchEngine(self.search_config)
        self.knowledge_graph = KnowledgeGraphEngine()
        self.reranker = SearchReranker(self.search_config)

        try:
            self.response_generator = ResponseGenerator(self.response_config)
            logger.info("Response generator initialized")
        except Exception as e:
            logger.error(f"Failed to initialize response generator: {e}")
            self.response_generator = None

        # Debug configuration
        self.debug_output = debug_output
        self.debug_dir = "debug_output"
        if self.debug_output:
            os.makedirs(self.debug_dir, exist_ok=True)

        logger.info("RAG Query Engine initialization complete")

    def get_query_embedding(self, query: str) -> List[float]:
        """Get embedding for the query text using vector search engine."""
        if self.vector_engine:
            return self.vector_engine.get_query_embedding(query)
        else:
            logger.warning("Vector engine not available, returning zero embedding")
            return [0.0] * 1024

    def vector_search(self, db: DatabaseConnection, query_embedding: List[float], limit: int = 10) -> List[Dict]:
        """Perform vector similarity search using vector search engine."""
        if self.vector_engine:
            return self.vector_engine.vector_search(db, query_embedding, limit)
        else:
            logger.warning("Vector engine not available")
            return []

    def text_search(self, db: DatabaseConnection, query: str, limit: int = 10) -> List[Dict]:
        """Perform full-text keyword search using text search engine."""
        return self.text_engine.text_search(db, query, limit)

    def merge_search_results(
            self,
            vector_results: List[Dict],
            text_results: List[Dict],
            vector_weight: float = None
        ) -> List[Dict]:
        """Merge and rank results from vector and text searches."""
        try:
            # Use config weight if not specified
            if vector_weight is None:
                vector_weight = self.search_config.get('vector_weight', 0.7)
            text_weight = 1 - vector_weight

            logger.info(f"Merging with weights - vector: {vector_weight}, text: {text_weight}")
            logger.info(f"Input results - vector: {len(vector_results)}, text: {len(text_results)}")

            # Normalize scores
            vec_max = max([r.get('score', 0) for r in vector_results]) if vector_results else 1.0
            txt_max = max([r.get('text_score', 0) for r in text_results]) if text_results else 1.0
            logger.info(f"Max scores - vector: {vec_max}, text: {txt_max}")

            # Create a map of doc_id to result for both result sets
            vector_map = {r['doc_id']: {
                **r,
                'vector_score': r.get('score', 0) / vec_max if vec_max else 0
            } for r in vector_results}

            text_map = {r['doc_id']: {
                **r,
                'text_score': r.get('text_score', 0) / txt_max if txt_max else 0
            } for r in text_results}

            logger.info(f"Unique docs - vector: {len(vector_map)}, text: {len(text_map)}")

            # Get all unique doc_ids
            all_doc_ids = set(vector_map.keys()) | set(text_map.keys())
            logger.info(f"Total unique docs before merging: {len(all_doc_ids)}")

            # Combine scores
            merged = []
            for doc_id in all_doc_ids:
                vector_result = vector_map.get(doc_id, {'vector_score': 0})
                text_result = text_map.get(doc_id, {'text_score': 0})

                combined_score = (
                    vector_weight * vector_result.get('vector_score', 0) +
                    text_weight * text_result.get('text_score', 0)
                )

                # Include all results initially
                merged.append({
                    'doc_id': doc_id,
                    'content': vector_result.get('content') or text_result.get('content'),
                    'vector_score': vector_result.get('vector_score', 0),
                    'text_score': text_result.get('text_score', 0),
                    'combined_score': combined_score
                })

            # Sort by combined score
            merged.sort(key=lambda x: x['combined_score'], reverse=True)

            # Take top results but ensure we have at least 3 sources
            min_sources = 8  # Increase to get more diverse results
            min_score = self.search_config.get('min_score_threshold', 0.01)  # Lower threshold

            # First, try to get sources above threshold
            filtered_results = [r for r in merged if r['combined_score'] >= min_score]

            # If we don't have enough results above threshold, take the top scoring ones regardless
            if len(filtered_results) < min_sources:
                filtered_results = merged[:min_sources]
                
            # For diverse results, ensure we include high text score results even if vector score is low
            text_focused_results = [r for r in merged if r['text_score'] > 0.5]
            if text_focused_results:
                # Add top text results that might have been missed
                for result in text_focused_results[:3]:
                    if result not in filtered_results:
                        filtered_results.append(result)

            logger.info(f"Total results after merging and filtering: {len(filtered_results)}")
            return filtered_results

        except Exception as e:
            logger.error(f"Error merging results: {str(e)}")
            raise

    def rerank_results(self, query: str, results: List[Dict]) -> tuple[List[Dict], Dict]:
        """Apply advanced reranking using the reranking engine."""
        return self.reranker.rerank_results(query, results)



    def preprocess_query(self, query: str) -> str:
        """
        Preprocess the query to improve search accuracy:
        1. Remove special characters but keep important punctuation
        2. Normalize whitespace
        3. Extract key concepts and expand with synonyms
        """
        # Clean and normalize
        query = re.sub(r'[^\w\s?.!,]', ' ', query)
        query = ' '.join(query.split())

        # Extract key concepts using OpenAI or Groq
        try:
            # Get model based on configuration
            if self.groq_api_key and self.response_config.get('groq_base_url'):
                model = self.response_config.get('query_expansion', {}).get('groq_model', 'mixtral-8x7b-32768')
                logger.info(f"Using Groq model for query expansion: {model}")
                response = self.response_client.chat.completions.create(
                    model=model,
                    messages=[{
                        "role": "system",
                        "content": "Extract and expand key concepts from the query. Format: concept1 | synonym1, synonym2 | concept2 | synonym1, synonym2. Limit your answers to less than 300 words"
                    }, {
                        "role": "user",
                        "content": query
                    }],
                    temperature=0.0
                )
            else:
                # Use response client (OpenAI or Mistral) for query expansion
                if hasattr(self.response_client, 'chat') and hasattr(self.response_client.chat, 'completions'):
                    # OpenAI-compatible client
                    model = self.response_config.get('query_expansion', {}).get('openai_model', 'gpt-4o')
                    logger.info(f"Using OpenAI-compatible model for query expansion: {model}")
                    response = self.response_client.chat.completions.create(
                        model=model,
                        messages=[{
                            "role": "system",
                            "content": "Extract and expand key concepts from the query. Format: concept1 | synonym1, synonym2 | concept2 | synonym1, synonym2"
                        }, {
                            "role": "user",
                            "content": query
                        }],
                        temperature=0.0
                    )
                else:
                    # Mistral client
                    model = self.response_config.get('query_expansion', {}).get('mistral_model', 'mistral-small-2503')
                    logger.info(f"Using Mistral model for query expansion: {model}")
                    response = self.response_client.chat.complete(
                        model=model,
                        messages=[{
                            "role": "system",
                            "content": "Extract and expand key concepts from the query. Format: concept1 | synonym1, synonym2 | concept2 | synonym1, synonym2"
                        }, {
                            "role": "user",
                            "content": query
                        }],
                        temperature=0.0
                    )

            # Parse expanded concepts
            expanded = response.choices[0].message.content
            expanded_terms = []
            for concept_group in expanded.split('|'):
                expanded_terms.extend(t.strip() for t in concept_group.split(','))

            # Combine original query with expanded terms
            enhanced_query = f"{query} {' '.join(expanded_terms)}"
            return enhanced_query.strip()

        except Exception as e:
            logger.warning(f"Query expansion failed: {str(e)}")
            return query

    def query(self, query_text: str, top_k: int = 5) -> SearchResponse:
        """Execute a hybrid search query combining vector and text search."""
        try:
            # Preprocess and enhance query
            enhanced_query = self.preprocess_query(query_text)
            logger.info(f"Enhanced query: {enhanced_query}")

            with DatabaseConnection() as db:
                # Get results from both search methods
                config_top_k = self.search_config.get('top_k', 20)  # Use config value, default to 20
                logger.info(f"Using config top_k: {config_top_k}")

                vector_results = self.vector_search(db, self.get_query_embedding(enhanced_query), limit=config_top_k)
                logger.info(f"Vector search returned {len(vector_results)} results")

                text_results = self.text_search(db, enhanced_query, limit=config_top_k)
                logger.info(f"Text search returned {len(text_results)} results")

                # Merge results
                merged_results = self.merge_search_results(vector_results, text_results)
                logger.info(f"After merging: {len(merged_results)} results")

                # Apply reranking model for better quality
                reranked_results, reranking_flow = self.rerank_results(query_text, merged_results)
                logger.info(f"After reranking: {len(reranked_results)} results")

                # Sort by reranked score and ensure minimum of 3 sources
                reranked_results.sort(key=lambda x: x.get('rerank_score', x['combined_score']), reverse=True)
                reranked_results = reranked_results[:max(top_k, 3)]  # Ensure at least 3 sources
                logger.info(f"After limiting to max(top_k, 3): {len(reranked_results)} results")

                # Build context with SearchResult objects
                formatted_results = []
                for doc in reranked_results:
                    # Get entities for this content
                    entities = self.knowledge_graph.get_entities_for_content(db, doc["content"])
                    logger.info(f"Found {len(entities)} entities for doc {doc['doc_id']}")

                    # Get relationships for these entities
                    relationships = self.knowledge_graph.get_relationships(db, [e.id for e in entities])
                    logger.info(f"Found {len(relationships)} relationships for doc {doc['doc_id']}")

                    # Create SearchResult object with reranking information
                    search_result = SearchResult(
                        doc_id=doc["doc_id"],
                        content=doc["content"],
                        vector_score=doc.get("vector_score", 0.0),
                        text_score=doc.get("text_score", 0.0),
                        combined_score=doc.get("rerank_score", doc["combined_score"]),  # Use rerank score
                        entities=entities,
                        relationships=relationships
                    )

                    # Add reranking metadata if available
                    if 'rerank_factors' in doc:
                        search_result.metadata = doc['rerank_factors']

                    formatted_results.append(search_result)

                # Create SearchResponse
                response = SearchResponse(
                    query=query_text,
                    results=formatted_results,
                    generated_response=self.response_generator.generate_response(query_text, {"results": formatted_results}) if self.response_generator else "Response generation not available",
                    execution_time=0.0,  # We'll set this in the API layer
                    reranking_flow=reranking_flow
                )

                logger.info(f"Final response has {len(response.results)} results")
                return response

        except Exception as e:
            logger.error(f"Query execution error: {str(e)}", exc_info=True)
            raise  # Let the API layer handle the error

    def _build_prompt(self, query: str, context: Dict) -> str:
        """Build prompt for the LLM using retrieved context."""
        PROMPT_PATH = os.path.join(os.path.dirname(__file__), 'prompts', 'rag.md')
        with open(PROMPT_PATH, 'r') as f:
            RAG_PROMPT_TEMPLATE = f.read()
        return config.get_response_prompt(query, context, RAG_PROMPT_TEMPLATE)

    def generate_response(self, query: str, context: Dict[str, Any]) -> str:
        """Generate a response using the response generator."""
        if self.response_generator:
            return self.response_generator.generate_response(query, context)
        else:
            return "Response generation not available due to missing API configuration."

    def get_entities_for_content(self, db: DatabaseConnection, content: str) -> List[Entity]:
        """Find entities mentioned in the content using knowledge graph engine."""
        return self.knowledge_graph.get_entities_for_content(db, content)

    def get_relationships(self, db: DatabaseConnection, entity_ids: List[int]) -> List[Relationship]:
        """Get relationships for the given entities using knowledge graph engine."""
        return self.knowledge_graph.get_relationships(db, entity_ids)

    def save_debug_output(self, stage: str, data: Dict) -> None:
        """Save intermediate results for debugging."""
        if not self.debug_output:
            return

        try:
            filename = f"rag_query_{stage}_{int(time.time())}.json"
            filepath = os.path.join(self.debug_dir, filename)

            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)

            logger.debug(f"Debug output for {stage} saved to {filepath}")

        except Exception as e:
            logger.warning(f"Failed to save debug output: {str(e)}")

    def hybrid_search(self, db: DatabaseConnection, query: str, top_k: int = 5) -> List[Dict]:
        try:
            # Get query embedding with fallback mechanism
            query_embedding = self.get_query_embedding(query)
            
            # Check if we're using a fallback zero embedding (API unavailable)
            using_fallback = all(v == 0.0 for v in query_embedding[:10] if query_embedding)  # Check first few values
            
            if using_fallback:
                logger.warning("Using text-only search due to embedding API unavailability")
                # Skip vector search and use only text search
                text_results = self.text_search(db, query, limit=top_k*3)
                return text_results[:top_k] if text_results else []
            
            # Normal flow with vector search
            vector_results = self.vector_search(db, query_embedding, limit=top_k*3)
            
            # Early exit for high confidence matches
            if len(vector_results) >= top_k and vector_results[0]['score'] > 0.9:
                return vector_results[:top_k]

            # Otherwise proceed with hybrid
            text_results = self.text_search(db, query, limit=top_k*2)
            return self.merge_search_results(vector_results, text_results)
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {str(e)}")
            # Last resort fallback - try text search only
            try:
                text_results = self.text_search(db, query, limit=top_k*3)
                return text_results[:top_k] if text_results else []
            except Exception as text_error:
                logger.error(f"Text search fallback also failed: {str(text_error)}")
                # Return empty results if all methods fail
                return []
