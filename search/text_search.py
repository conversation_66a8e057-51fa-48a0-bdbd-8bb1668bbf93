"""
Full-text search module.

This module handles keyword-based text search operations with support for
Chinese queries, phrase matching, and proximity search.
"""

import logging
import re
from typing import List, Dict
from db import DatabaseConnection

logger = logging.getLogger(__name__)


class TextSearchEngine:
    """Handles full-text keyword search operations."""
    
    def __init__(self, search_config: Dict):
        """Initialize the text search engine."""
        self.search_config = search_config
        logger.info("Text search engine initialized")
    
    def text_search(self, db: DatabaseConnection, query: str, limit: int = 10) -> List[Dict]:
        """
        Perform full-text keyword search with Chinese language support.
        
        Args:
            db: Database connection
            query: Search query text
            limit: Maximum number of results to return
            
        Returns:
            List of search results with doc_id, content, and text_score
        """
        try:
            # Preprocess query
            processed_query = self._preprocess_query(query)
            if not processed_query:
                logger.info("No valid search terms found, returning empty results")
                return []
            
            # Detect if this is a Chinese query
            is_chinese_query = self._is_chinese_query(query)
            
            if is_chinese_query:
                return self._chinese_text_search(db, query, limit)
            else:
                return self._english_text_search(db, processed_query, limit)
                
        except Exception as e:
            logger.error(f"Error in text search: {str(e)}")
            return []
    
    def _preprocess_query(self, query: str) -> str:
        """Preprocess query for text search."""
        # Limit query length to prevent parser errors
        max_query_length = self.search_config.get('max_query_length', 500)
        if len(query) > max_query_length:
            # Take first N chars of original query + important keywords
            words = query.split()
            base_query = ' '.join(words[:10])  # First 10 words
            important_words = [w for w in words[10:] if len(w) > 3][:20]  # Up to 20 important keywords
            query = f"{base_query} {' '.join(important_words)}"
            logger.info(f"Query truncated to: {query}")
        
        # Replace hyphens with spaces for better matching
        return query.replace('-', ' ')
    
    def _is_chinese_query(self, query: str) -> bool:
        """Check if the query contains Chinese characters."""
        return bool(re.findall(r'[\u4e00-\u9fff]+', query))
    
    def _chinese_text_search(self, db: DatabaseConnection, query: str, limit: int) -> List[Dict]:
        """
        Perform text search optimized for Chinese queries.
        
        Args:
            db: Database connection
            query: Chinese query text
            limit: Maximum number of results
            
        Returns:
            List of search results
        """
        try:
            # Extract Chinese characters from query
            chinese_chars = re.findall(r'[\u4e00-\u9fff]+', query)
            
            if not chinese_chars:
                return []
            
            # Use the first Chinese term for search
            search_term = chinese_chars[0]
            
            # SQL optimized for Chinese content with stock code matching
            sql = """
                SELECT DISTINCT
                    doc_id,
                    content,
                    1.0 as text_score
                FROM Document_Embeddings
                WHERE (content LIKE CONCAT('%', %s, '%')
                    OR content REGEXP '[0-9]{4}')  -- Match stock codes
                    AND LENGTH(content) > 100
                ORDER BY CHAR_LENGTH(content) ASC
                LIMIT %s;
            """
            
            results = db.execute_query(sql, (search_term, limit))
            
            search_results = [
                {
                    "doc_id": r[0],
                    "content": r[1],
                    "text_score": float(r[2])
                }
                for r in results
            ]
            
            logger.info(f"Chinese text search found {len(search_results)} results for term: {search_term}")
            return search_results
            
        except Exception as e:
            logger.error(f"Error in Chinese text search: {str(e)}")
            return []
    
    def _english_text_search(self, db: DatabaseConnection, query: str, limit: int) -> List[Dict]:
        """
        Perform text search optimized for English queries.
        
        Args:
            db: Database connection
            query: Processed English query
            limit: Maximum number of results
            
        Returns:
            List of search results
        """
        try:
            # Extract search terms
            search_terms = self._extract_search_terms(query)
            
            if not search_terms:
                return []
            
            # Use the first meaningful term for search
            search_term = search_terms[0]
            
            # SQL for English content search
            sql = """
                SELECT DISTINCT
                    doc_id,
                    content,
                    1.0 as text_score
                FROM Document_Embeddings
                WHERE content LIKE CONCAT('%', %s, '%')
                    AND LENGTH(content) > 100
                ORDER BY CHAR_LENGTH(content) ASC
                LIMIT %s;
            """
            
            results = db.execute_query(sql, (search_term, limit))
            
            search_results = [
                {
                    "doc_id": r[0],
                    "content": r[1],
                    "text_score": float(r[2])
                }
                for r in results
            ]
            
            logger.info(f"English text search found {len(search_results)} results for term: {search_term}")
            return search_results
            
        except Exception as e:
            logger.error(f"Error in English text search: {str(e)}")
            return []
    
    def _extract_search_terms(self, query: str) -> List[str]:
        """Extract meaningful search terms from English query."""
        # Extract key phrases (quoted terms)
        key_phrases = re.findall(r'"([^"]*)"', query)
        remaining_text = re.sub(r'"[^"]*"', '', query)
        
        # Split remaining text into terms and clean them
        terms = set()  # Use set to deduplicate
        
        for t in remaining_text.split():
            t = t.strip()
            if t:
                # Escape special characters that could break the parser
                t = re.sub(r'[+\-=&|><!(){}[\]^"~*?:/\\]', ' ', t)
                t = t.strip()
                if t and len(t) > 2:  # Only add words longer than 2 chars
                    terms.add(t.lower())  # Normalize to lowercase
        
        # Combine phrases and terms
        search_terms = key_phrases + list(terms)
        return [term for term in search_terms if term and len(term) > 2]
    
    def _build_fulltext_query(self, query: str) -> str:
        """
        Build a full-text search query with semantic operators.
        
        Note: This method is kept for potential future use with FTS,
        but current implementation uses simpler LIKE queries for reliability.
        """
        # Extract key phrases (quoted terms)
        key_phrases = re.findall(r'"([^"]*)"', query)
        remaining_text = re.sub(r'"[^"]*"', '', query)
        
        # Split remaining text into terms and clean them
        terms = set()
        multi_word_terms = set()
        
        for t in remaining_text.split():
            t = t.strip()
            if t:
                # Escape special characters
                t = re.sub(r'[+\-=&|><!(){}[\]^"~*?:/\\]', ' ', t)
                t = t.strip()
                if t:
                    if ' ' in t:
                        multi_word_terms.add(t)
                    elif len(t) > 2:
                        terms.add(t.lower())
        
        # Build search expression with semantic operators
        search_parts = []
        
        # Add exact phrases with high weight
        for phrase in key_phrases:
            if phrase:
                phrase = re.sub(r'[+\-=&|><!(){}[\]^"~*?:/\\]', ' ', phrase)
                phrase = phrase.strip()
                if phrase:
                    search_parts.append(f'content:"{phrase}">>{self.search_config.get("exact_phrase_weight", 2.0)}')
        
        # Add multi-word terms as phrases
        for term in multi_word_terms:
            search_parts.append(f'content:"{term}">>{self.search_config.get("exact_phrase_weight", 2.0)}')
        
        # Add individual terms with proximity search
        if terms:
            proximity_terms = list(terms)[:5]
            terms_str = ' '.join(proximity_terms)
            if terms_str:
                search_parts.append(f'content:"{terms_str}"~{self.search_config.get("proximity_distance", 5)}')
            
            # Add individual terms with lower weight
            for term in terms:
                search_parts.append(f'content:{term}>>{self.search_config.get("single_term_weight", 1.5)}')
        
        # Combine all parts with OR
        if search_parts:
            search_parts = search_parts[:25]  # Limit to prevent parser errors
            valid_parts = [part for part in search_parts if part and part.startswith('content:')]
            
            if valid_parts:
                return '(' + ' OR '.join(valid_parts) + ') IN BOOLEAN MODE'
        
        return ""
    
    def search_by_keywords(self, db: DatabaseConnection, keywords: List[str], 
                          limit: int = 10) -> List[Dict]:
        """
        Search for documents containing specific keywords.
        
        Args:
            db: Database connection
            keywords: List of keywords to search for
            limit: Maximum number of results
            
        Returns:
            List of search results
        """
        try:
            if not keywords:
                return []
            
            # Build WHERE clause for multiple keywords
            where_conditions = []
            params = []
            
            for keyword in keywords:
                where_conditions.append("content LIKE CONCAT('%', %s, '%')")
                params.append(keyword)
            
            sql = f"""
                SELECT DISTINCT doc_id, content, 1.0 as text_score
                FROM Document_Embeddings
                WHERE ({' OR '.join(where_conditions)})
                    AND LENGTH(content) > 100
                ORDER BY CHAR_LENGTH(content) ASC
                LIMIT %s;
            """
            
            params.append(limit)
            results = db.execute_query(sql, tuple(params))
            
            return [
                {
                    "doc_id": r[0],
                    "content": r[1],
                    "text_score": float(r[2])
                }
                for r in results
            ]
            
        except Exception as e:
            logger.error(f"Error in keyword search: {str(e)}")
            return []
    
    def search_exact_phrase(self, db: DatabaseConnection, phrase: str, 
                           limit: int = 10) -> List[Dict]:
        """
        Search for documents containing an exact phrase.
        
        Args:
            db: Database connection
            phrase: Exact phrase to search for
            limit: Maximum number of results
            
        Returns:
            List of search results
        """
        try:
            sql = """
                SELECT DISTINCT doc_id, content, 1.0 as text_score
                FROM Document_Embeddings
                WHERE content LIKE CONCAT('%', %s, '%')
                    AND LENGTH(content) > 100
                ORDER BY CHAR_LENGTH(content) ASC
                LIMIT %s;
            """
            
            results = db.execute_query(sql, (phrase, limit))
            
            return [
                {
                    "doc_id": r[0],
                    "content": r[1],
                    "text_score": float(r[2])
                }
                for r in results
            ]
            
        except Exception as e:
            logger.error(f"Error in exact phrase search: {str(e)}")
            return []
