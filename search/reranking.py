"""
Advanced reranking module for search results.

This module implements a sophisticated 7-factor reranking system that considers:
1. Base relevance score (vector + text combined)
2. Content quality and completeness
3. Language matching (Chinese/English optimization)
4. Domain-specific relevance (financial/stock data)
5. Entity density and knowledge graph richness
6. Query term coverage effectiveness
7. Content structure quality (tables, lists, etc.)
"""

import logging
from typing import Dict, List, Tuple

logger = logging.getLogger(__name__)


class SearchReranker:
    """Advanced reranking system for search results."""
    
    def __init__(self, search_config: Dict):
        """Initialize the reranker with configuration."""
        self.search_config = search_config
        self.rerank_config = search_config.get('reranking', {})
        
    def rerank_results(self, query: str, results: List[Dict]) -> Tuple[List[Dict], Dict]:
        """
        Apply advanced reranking to search results.
        
        Args:
            query: Original search query
            results: List of search results to rerank
            
        Returns:
            Tuple of (reranked_results, reranking_flow_info)
        """
        try:
            # Initialize reranking flow tracking
            reranking_flow = {
                'enabled': True,
                'query_analysis': {},
                'factor_weights': {},
                'results_analysis': [],
                'performance_metrics': {},
                'summary': {}
            }
            
            if not results:
                reranking_flow['enabled'] = False
                reranking_flow['summary']['message'] = "No results to rerank"
                return results, reranking_flow
            
            # Check if reranking is enabled
            if not self.rerank_config.get('enabled', True):
                logger.info("Reranking disabled in configuration")
                reranking_flow['enabled'] = False
                reranking_flow['summary']['message'] = "Reranking disabled in configuration"
                return results, reranking_flow
            
            logger.info(f"Reranking {len(results)} results for query: '{query}'")
            
            # Analyze query and get configuration
            query_analysis = self._analyze_query(query)
            weights = self.rerank_config.get('weights', {})
            
            # Track analysis in flow
            reranking_flow['query_analysis'] = query_analysis
            reranking_flow['factor_weights'] = weights
            
            # Process each result
            reranked_results = []
            for i, result in enumerate(results):
                reranked_result, result_analysis = self._rerank_single_result(
                    query, result, query_analysis, weights, i
                )
                reranked_results.append(reranked_result)
                reranking_flow['results_analysis'].append(result_analysis)
            
            # Calculate performance metrics
            reranking_flow['performance_metrics'] = self._calculate_performance_metrics(
                results, reranked_results, reranking_flow['results_analysis']
            )
            
            # Generate summary
            reranking_flow['summary'] = self._generate_summary(
                results, weights, query_analysis, reranking_flow['performance_metrics']
            )
            
            return reranked_results, reranking_flow
            
        except Exception as e:
            logger.error(f"Error in reranking: {str(e)}")
            error_flow = {
                'enabled': False,
                'error': str(e),
                'summary': {'message': f"Reranking failed: {str(e)}"}
            }
            return results, error_flow
    
    def _analyze_query(self, query: str) -> Dict:
        """Analyze query characteristics for reranking."""
        domain_terms = self.rerank_config.get('domain_terms', {}).get('financial', [])
        
        is_chinese_query = any('\u4e00' <= char <= '\u9fff' for char in query)
        is_financial_query = any(term in query.lower() for term in domain_terms)
        
        return {
            'original_query': query,
            'query_length': len(query),
            'is_chinese_query': is_chinese_query,
            'is_financial_query': is_financial_query,
            'detected_financial_terms': [term for term in domain_terms if term in query.lower()],
            'language_detected': 'Chinese' if is_chinese_query else 'English'
        }
    
    def _rerank_single_result(self, query: str, result: Dict, query_analysis: Dict, 
                             weights: Dict, index: int) -> Tuple[Dict, Dict]:
        """Rerank a single result and return analysis."""
        content = result.get('content', '')
        vector_score = result.get('vector_score', 0)
        text_score = result.get('text_score', 0)
        combined_score = result.get('combined_score', 0)
        doc_id = result.get('doc_id', f'doc_{index}')
        
        # Calculate all reranking factors
        factors = self._calculate_all_factors(query, content, query_analysis)

        # Set the base score from the result
        factors['base_score'] = combined_score

        # Weighted combination using configuration
        rerank_score = sum(
            weights.get(factor_name, 0.0) * factor_value
            for factor_name, factor_value in factors.items()
        )
        
        # Create analysis record
        result_analysis = {
            'doc_id': doc_id,
            'original_scores': {
                'vector_score': vector_score,
                'text_score': text_score,
                'combined_score': combined_score
            },
            'reranking_factors': factors,
            'weighted_contributions': {
                factor_name: weights.get(factor_name, 0.0) * factor_value
                for factor_name, factor_value in factors.items()
            },
            'final_rerank_score': rerank_score,
            'score_improvement': rerank_score - combined_score,
            'content_preview': content[:200] + '...' if len(content) > 200 else content
        }
        
        # Update result with rerank score and factors
        reranked_result = result.copy()
        reranked_result['rerank_score'] = rerank_score
        reranked_result['rerank_factors'] = factors
        
        return reranked_result, result_analysis
    
    def _calculate_all_factors(self, query: str, content: str, query_analysis: Dict) -> Dict:
        """Calculate all reranking factors for a piece of content."""
        return {
            'base_score': 0.0,  # Will be set by caller
            'content_quality': self._calculate_content_quality(content),
            'language_bonus': self._calculate_language_bonus(content, query_analysis),
            'domain_bonus': self._calculate_domain_bonus(content, query_analysis),
            'entity_density': self._calculate_entity_density(content),
            'term_coverage': self._calculate_term_coverage(query, content),
            'structure_bonus': self._calculate_structure_bonus(content)
        }
    
    def _calculate_content_quality(self, content: str) -> float:
        """Calculate content quality based on length, structure, and information density."""
        if not content:
            return 0.0
        
        # Length factor (optimal range: 200-2000 characters)
        length = len(content)
        if length < 50:
            length_score = length / 50.0
        elif length <= 2000:
            length_score = 1.0
        else:
            length_score = max(0.5, 2000 / length)
        
        # Information density (ratio of meaningful words to total words)
        words = content.split()
        if not words:
            return length_score * 0.5
        
        meaningful_words = [w for w in words if len(w) > 2 and not w.isdigit()]
        density_score = len(meaningful_words) / len(words) if words else 0
        
        # Structural indicators (tables, lists, organized data)
        structure_indicators = content.count('|') + content.count('\n') + content.count(':')
        structure_score = min(1.0, structure_indicators / 20.0)
        
        return (length_score * 0.4 + density_score * 0.4 + structure_score * 0.2)
    
    def _calculate_language_bonus(self, content: str, query_analysis: Dict) -> float:
        """Calculate bonus for language matching between query and content."""
        if not content:
            return 0.0
        
        # Get language matching configuration
        lang_config = self.rerank_config.get('language_matching', {})
        is_chinese_query = query_analysis.get('is_chinese_query', False)
        
        # Check if content contains Chinese characters
        has_chinese_content = any('\u4e00' <= char <= '\u9fff' for char in content)
        
        if is_chinese_query and has_chinese_content:
            return lang_config.get('chinese_chinese_bonus', 1.0)
        elif is_chinese_query and not has_chinese_content:
            return lang_config.get('chinese_english_penalty', 0.3)
        elif not is_chinese_query and has_chinese_content:
            return lang_config.get('english_chinese_penalty', 0.7)
        else:
            return lang_config.get('english_english_neutral', 0.8)
    
    def _calculate_domain_bonus(self, content: str, query_analysis: Dict) -> float:
        """Calculate bonus for domain-specific relevance."""
        if not content:
            return 0.5
        
        is_financial_query = query_analysis.get('is_financial_query', False)
        if not is_financial_query:
            return 0.5
        
        # Financial/stock-related terms in content
        financial_terms = self.rerank_config.get('domain_terms', {}).get('financial', [])
        
        content_lower = content.lower()
        matches = sum(1 for term in financial_terms if term in content_lower)
        
        # Bonus based on financial term density
        return min(1.0, matches / 5.0)
    
    def _calculate_entity_density(self, content: str) -> float:
        """Calculate entity density as a proxy for information richness."""
        if not content:
            return 0.0
        
        words = content.split()
        potential_entities = 0
        
        for word in words:
            # Stock codes (4-digit numbers)
            if word.isdigit() and len(word) == 4:
                potential_entities += 2  # Stock codes are highly valuable
            # Capitalized words (potential company names, etc.)
            elif word and word[0].isupper() and len(word) > 2:
                potential_entities += 1
            # Numbers with decimal points (prices, percentages)
            elif '.' in word and any(c.isdigit() for c in word):
                potential_entities += 1
        
        # Normalize by content length
        return min(1.0, potential_entities / max(1, len(words) / 10))
    
    def _calculate_term_coverage(self, query: str, content: str) -> float:
        """Calculate how well the content covers the query terms."""
        if not query or not content:
            return 0.0
        
        # Extract meaningful terms from query
        query_terms = set()
        for word in query.split():
            if len(word) > 2 and not word.isdigit():
                query_terms.add(word.lower())
        
        if not query_terms:
            return 0.5
        
        # Check coverage in content
        content_lower = content.lower()
        covered_terms = sum(1 for term in query_terms if term in content_lower)
        
        return covered_terms / len(query_terms)
    
    def _calculate_structure_bonus(self, content: str) -> float:
        """Calculate bonus for well-structured content (tables, lists, etc.)."""
        if not content:
            return 0.0
        
        structure_score = 0.0
        
        # Table indicators
        if '|' in content and content.count('|') > 5:
            structure_score += 0.4
        
        # List indicators
        if content.count('\n') > 3:
            structure_score += 0.3
        
        # Key-value pairs
        if content.count(':') > 2:
            structure_score += 0.2
        
        # Organized data (numbers, codes)
        if len([w for w in content.split() if w.isdigit()]) > 5:
            structure_score += 0.1
        
        return min(1.0, structure_score)
    
    def _calculate_performance_metrics(self, original_results: List[Dict], 
                                     reranked_results: List[Dict], 
                                     results_analysis: List[Dict]) -> Dict:
        """Calculate performance metrics for the reranking process."""
        original_scores = [r.get('combined_score', 0) for r in original_results]
        rerank_scores = [r.get('rerank_score', 0) for r in reranked_results]
        
        return {
            'total_results_processed': len(original_results),
            'original_score_range': {
                'min': min(original_scores) if original_scores else 0,
                'max': max(original_scores) if original_scores else 0,
                'avg': sum(original_scores) / len(original_scores) if original_scores else 0
            },
            'reranked_score_range': {
                'min': min(rerank_scores) if rerank_scores else 0,
                'max': max(rerank_scores) if rerank_scores else 0,
                'avg': sum(rerank_scores) / len(rerank_scores) if rerank_scores else 0
            },
            'score_improvements': [r['score_improvement'] for r in results_analysis],
            'avg_score_improvement': sum(r['score_improvement'] for r in results_analysis) / len(results_analysis) if results_analysis else 0
        }
    
    def _generate_summary(self, results: List[Dict], weights: Dict, 
                         query_analysis: Dict, performance_metrics: Dict) -> Dict:
        """Generate a summary of the reranking process."""
        return {
            'message': f"Successfully reranked {len(results)} results",
            'top_contributing_factor': max(weights.items(), key=lambda x: x[1])[0] if weights else 'base_score',
            'language_optimization': 'Applied Chinese language bonus' if query_analysis.get('is_chinese_query') else 'Applied English language processing',
            'domain_optimization': 'Applied financial domain bonus' if query_analysis.get('is_financial_query') else 'No domain-specific optimization',
            'overall_improvement': 'Positive' if performance_metrics.get('avg_score_improvement', 0) > 0 else 'Neutral'
        }
