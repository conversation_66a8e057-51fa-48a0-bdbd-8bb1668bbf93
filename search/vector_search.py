"""
Vector similarity search module.

This module handles vector embeddings and similarity search operations
using Mistral AI embeddings and SingleStore vector database.
"""

import os
import logging
from typing import List, Dict
from mistralai import Mistral
from db import DatabaseConnection

logger = logging.getLogger(__name__)


class VectorSearchEngine:
    """Handles vector embedding generation and similarity search."""
    
    def __init__(self, embedding_config: Dict):
        """Initialize the vector search engine."""
        self.embedding_config = embedding_config
        self.embedding_model = embedding_config.get('model', 'mistral-embed')
        
        # Initialize Mistral client for embeddings
        self.mistral_api_key = os.getenv("MISTRAL_API_KEY")
        if not self.mistral_api_key:
            logger.error("MISTRAL_API_KEY environment variable is missing")
            raise ValueError("MISTRAL_API_KEY environment variable is required for embeddings")
        
        self.embedding_client = Mistral(api_key=self.mistral_api_key)
        logger.info(f"Vector search engine initialized with model: {self.embedding_model}")
    
    def get_query_embedding(self, query: str) -> List[float]:
        """
        Generate embedding vector for the query text using Mistral.
        
        Args:
            query: Text query to embed
            
        Returns:
            List of float values representing the embedding vector
        """
        try:
            response = self.embedding_client.embeddings.create(
                model=self.embedding_model,
                inputs=[query]
            )
            embedding = response.data[0].embedding
            logger.debug(f"Generated embedding for query: '{query[:50]}...' (dimension: {len(embedding)})")
            return embedding
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"Error getting query embedding: {error_msg}")
            
            # Check for API key issues
            if "api key" in error_msg.lower():
                logger.warning("Mistral API key issue detected. Using fallback search method.")
                # Return a placeholder embedding vector of all zeros with Mistral's dimension (1024)
                # This will allow the search to continue with text search only
                return [0.0] * 1024
            else:
                # For other errors, raise the exception
                raise
    
    def vector_search(self, db: DatabaseConnection, query_embedding: List[float], 
                     limit: int = 10) -> List[Dict]:
        """
        Perform vector similarity search against the database.
        
        Args:
            db: Database connection
            query_embedding: Query embedding vector
            limit: Maximum number of results to return
            
        Returns:
            List of search results with doc_id, content, and similarity score
        """
        try:
            # Check if we're using a fallback zero embedding (API unavailable)
            using_fallback = all(v == 0.0 for v in query_embedding[:10] if query_embedding)
            
            if using_fallback:
                logger.warning("Skipping vector search due to embedding API unavailability")
                return []
            
            # Format vector for SQL
            vector_param = "[" + ",".join(f"{x:.6f}" for x in query_embedding) + "]"
            
            # Set vector parameter (Mistral embeddings are 1024 dimensions)
            embedding_size = len(query_embedding)
            db.execute_query(f"SET @qvec = %s :> VECTOR({embedding_size});", (vector_param,))
            
            # Execute similarity search with diversity
            vector_search_sql = """
                SELECT DISTINCT doc_id, content, (embedding <*> @qvec) AS score
                FROM Document_Embeddings
                ORDER BY score DESC
                LIMIT %s;
            """
            
            results = db.execute_query(vector_search_sql, (limit,))
            
            if results is None:
                logger.warning("Vector search returned no results")
                return []
            
            search_results = [
                {
                    "doc_id": r[0], 
                    "content": r[1], 
                    "score": float(r[2])
                }
                for r in results
            ]
            
            logger.info(f"Vector search found {len(search_results)} results")
            if search_results:
                logger.debug(f"Top vector score: {search_results[0]['score']:.4f}")
            
            return search_results
            
        except Exception as e:
            logger.error(f"Error in vector search: {str(e)}")
            raise
    
    def is_embedding_available(self) -> bool:
        """
        Check if embedding service is available.
        
        Returns:
            True if embedding service is available, False otherwise
        """
        try:
            # Test with a simple query
            test_embedding = self.get_query_embedding("test")
            return not all(v == 0.0 for v in test_embedding[:10])
        except Exception:
            return False
    
    def get_embedding_dimension(self) -> int:
        """
        Get the dimension of embeddings produced by the current model.
        
        Returns:
            Embedding dimension (1024 for Mistral)
        """
        return 1024  # Mistral embeddings are 1024-dimensional
    
    def batch_embed(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in batch.
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        try:
            if not texts:
                return []
            
            response = self.embedding_client.embeddings.create(
                model=self.embedding_model,
                inputs=texts
            )
            
            embeddings = [data.embedding for data in response.data]
            logger.info(f"Generated {len(embeddings)} embeddings in batch")
            return embeddings
            
        except Exception as e:
            logger.error(f"Error in batch embedding: {str(e)}")
            # Return zero embeddings as fallback
            return [[0.0] * 1024 for _ in texts]
    
    def calculate_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        Calculate cosine similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            Cosine similarity score between -1 and 1
        """
        try:
            if len(embedding1) != len(embedding2):
                raise ValueError("Embeddings must have the same dimension")
            
            # Calculate dot product
            dot_product = sum(a * b for a, b in zip(embedding1, embedding2))
            
            # Calculate magnitudes
            magnitude1 = sum(a * a for a in embedding1) ** 0.5
            magnitude2 = sum(b * b for b in embedding2) ** 0.5
            
            # Avoid division by zero
            if magnitude1 == 0 or magnitude2 == 0:
                return 0.0
            
            # Calculate cosine similarity
            similarity = dot_product / (magnitude1 * magnitude2)
            return similarity
            
        except Exception as e:
            logger.error(f"Error calculating similarity: {str(e)}")
            return 0.0
    
    def find_similar_documents(self, db: DatabaseConnection, target_doc_id: int, 
                              limit: int = 5) -> List[Dict]:
        """
        Find documents similar to a target document.
        
        Args:
            db: Database connection
            target_doc_id: ID of the target document
            limit: Maximum number of similar documents to return
            
        Returns:
            List of similar documents with similarity scores
        """
        try:
            # Get the embedding of the target document
            target_sql = "SELECT embedding FROM Document_Embeddings WHERE doc_id = %s"
            target_result = db.execute_query(target_sql, (target_doc_id,))
            
            if not target_result:
                logger.warning(f"No embedding found for document {target_doc_id}")
                return []
            
            target_embedding = target_result[0][0]
            
            # Find similar documents
            similar_sql = """
                SELECT doc_id, content, (embedding <*> %s) AS similarity
                FROM Document_Embeddings
                WHERE doc_id != %s
                ORDER BY similarity DESC
                LIMIT %s;
            """
            
            results = db.execute_query(similar_sql, (target_embedding, target_doc_id, limit))
            
            return [
                {
                    "doc_id": r[0],
                    "content": r[1],
                    "similarity": float(r[2])
                }
                for r in results
            ]
            
        except Exception as e:
            logger.error(f"Error finding similar documents: {str(e)}")
            return []
